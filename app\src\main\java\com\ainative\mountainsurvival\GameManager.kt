package com.ainative.mountainsurvival

import android.util.Log

/**
 * 游戏状态管理器单例
 * 负责管理玩家的所有状态和游戏逻辑的核心模块
 */
object GameManager {

    private const val TAG = "GameManager"

    /**
     * 游戏状态
     * 包含所有核心游戏数值
     */
    var gameState: GameState = GameState()
        private set

    /**
     * 游戏是否已初始化
     */
    private var isInitialized = false

    /**
     * 游戏历史记录（用于回溯和分析）
     */
    private val gameHistory = mutableListOf<GameStateSnapshot>()

    /**
     * 初始化游戏
     * 设置 gameState 的所有初始值
     */
    fun initializeGame() {
        Log.d(TAG, "初始化游戏状态...")

        gameState = GameState(
            warmth = 80,
            stamina = 100,
            firewood = 5,
            food = 3,
            currentDay = 1,
            cabinIntegrity = 40,
            hope = 50,
            specialItems = mutableSetOf(),
            roofLeaking = false
        )

        // 清空历史记录
        gameHistory.clear()

        // 记录初始状态
        saveStateSnapshot("游戏开始")

        isInitialized = true

        Log.d(TAG, "游戏初始化完成: $gameState")
    }

    /**
     * 应用选择效果
     * 接收一个 Choice 对象，并根据其 effects Map 来更新 gameState 中对应的数值
     *
     * @param choice 玩家选择的 Choice 对象
     * @return 应用效果后的状态变化描述
     */
    fun applyChoice(choice: Choice): ChoiceResult {
        if (!isInitialized) {
            Log.w(TAG, "游戏未初始化，无法应用选择")
            return ChoiceResult(
                success = false,
                message = "游戏未初始化",
                stateChanges = emptyMap()
            )
        }

        Log.d(TAG, "应用选择: ${choice.text}")
        Log.d(TAG, "选择效果: ${choice.effects}")

        // 记录应用前的状态
        val beforeState = gameState.copy()

        // 应用效果
        val stateChanges = mutableMapOf<String, Pair<Int, Int>>() // 属性名 -> (旧值, 新值)

        choice.effects.forEach { (property, change) ->
            val oldValue = getPropertyValue(property)
            val changeValue = convertToInt(change)
            gameState.applyEffects(mapOf(property to changeValue))
            val newValue = getPropertyValue(property)

            if (oldValue != newValue) {
                stateChanges[property] = Pair(oldValue, newValue)
                Log.d(TAG, "属性 $property: $oldValue -> $newValue (变化: ${if (changeValue >= 0) "+$changeValue" else "$changeValue"})")
            }
        }

        // 处理特殊物品奖励
        var specialItemGained: String? = null
        choice.specialItemReward?.let { item ->
            if (gameState.specialItems.add(item)) {
                Log.d(TAG, "获得特殊物品: $item")
                specialItemGained = item
            }
        }

        // 处理specialItemGained字段（JSON兼容性）
        choice.specialItemGained?.let { item ->
            if (gameState.specialItems.add(item)) {
                Log.d(TAG, "获得特殊物品: $item")
                specialItemGained = item
            }
        }

        // 处理特殊物品消耗
        choice.specialItemUsed?.let { item ->
            if (gameState.specialItems.remove(item)) {
                Log.d(TAG, "使用特殊物品: $item")
            } else {
                Log.w(TAG, "尝试使用不存在的特殊物品: $item")
            }
        }

        // 处理特殊效果
        choice.specialEffects?.let { effects ->
            effects.forEach { (key, value) ->
                when (key) {
                    "roofLeaking" -> {
                        gameState.roofLeaking = value as Boolean
                        Log.d(TAG, "设置屋顶漏雪状态: $value")
                    }
                    else -> {
                        Log.w(TAG, "未知的特殊效果: $key = $value")
                    }
                }
            }
        }

        // 记录状态快照
        saveStateSnapshot("应用选择: ${choice.text}")

        // 检查游戏结束条件
        val gameOverResult = getGameOverResult()

        val result = ChoiceResult(
            success = true,
            message = "选择应用成功",
            stateChanges = stateChanges,
            specialItemGained = specialItemGained,
            gameOverResult = gameOverResult
        )

        Log.d(TAG, "选择应用完成: ${result.message}")

        return result
    }

    /**
     * 获取属性值
     * @param property 属性名
     * @return 属性值
     */
    private fun getPropertyValue(property: String): Int {
        return when (property) {
            "warmth" -> gameState.warmth
            "stamina" -> gameState.stamina
            "firewood" -> gameState.firewood
            "food" -> gameState.food
            "cabin_integrity" -> gameState.cabinIntegrity
            "hope" -> gameState.hope
            "currentDay" -> gameState.currentDay
            else -> 0
        }
    }

    /**
     * 安全地将Any类型转换为Int
     * 处理JSON解析时可能出现的Double类型
     */
    private fun convertToInt(value: Any): Int {
        return when (value) {
            is Int -> value
            is Double -> value.toInt()
            is Float -> value.toInt()
            is Long -> value.toInt()
            is String -> value.toIntOrNull() ?: 0
            else -> 0
        }
    }

    /**
     * 获取游戏结束结果
     * @return 游戏结束结果，如果游戏未结束则返回null
     */
    fun getGameOverResult(): GameOverResult? {
        val (isGameOver, reason) = gameState.checkGameOver()

        return if (isGameOver) {
            val isVictory = reason == "成功存活"
            GameOverResult(
                isGameOver = true,
                isVictory = isVictory,
                reason = reason,
                finalDay = gameState.currentDay,
                finalStats = gameState.copy()
            )
        } else {
            null
        }
    }

    /**
     * 执行夜晚结算
     * 根据游戏规则自动处理夜晚的数值变化
     */
    fun performNightSettlement(): NightSettlementResult {
        Log.d(TAG, "执行夜晚结算...")

        val beforeWarmth = gameState.warmth
        val beforeFirewood = gameState.firewood

        // 执行夜晚结算
        gameState.nightTimeSettlement()

        val warmthChange = gameState.warmth - beforeWarmth
        val firewoodChange = gameState.firewood - beforeFirewood

        // 记录状态快照
        saveStateSnapshot("夜晚结算")

        val result = NightSettlementResult(
            firewoodUsed = -firewoodChange,
            warmthChange = warmthChange,
            hadFirewood = beforeFirewood >= 5,
            finalWarmth = gameState.warmth,
            finalFirewood = gameState.firewood
        )

        Log.d(TAG, "夜晚结算完成: $result")

        return result
    }

    /**
     * 进食
     * @return 进食结果
     */
    fun eatFood(): EatResult {
        val beforeFood = gameState.food
        val beforeStamina = gameState.stamina

        val success = gameState.eat()

        if (success) {
            saveStateSnapshot("进食")
            Log.d(TAG, "进食成功: 食物 $beforeFood -> ${gameState.food}, 体力 $beforeStamina -> ${gameState.stamina}")
        } else {
            Log.d(TAG, "进食失败: 没有食物")
        }

        return EatResult(
            success = success,
            foodUsed = if (success) 1 else 0,
            staminaGained = if (success) gameState.stamina - beforeStamina else 0,
            remainingFood = gameState.food
        )
    }

    /**
     * 执行夜晚阶段
     * 根据规则（木柴≥5时消耗5木柴体温+10；木柴<5时体温-10）更新状态，并将 currentDay 加一
     */
    fun performNightPhase(): NightPhaseResult {
        Log.d(TAG, "执行夜晚阶段...")

        val beforeWarmth = gameState.warmth
        val beforeFirewood = gameState.firewood
        val beforeStamina = gameState.stamina
        val beforeDay = gameState.currentDay

        // 执行夜晚结算规则
        if (gameState.firewood >= 5) {
            // 木柴足够：消耗5木柴，体温+10
            gameState.firewood -= 5
            gameState.warmth = (gameState.warmth + 10).coerceAtMost(100)
            Log.d(TAG, "木柴足够：消耗5木柴，体温+10")
        } else {
            // 木柴不足：体温-20
            gameState.warmth = (gameState.warmth - 20).coerceAtLeast(0)
            Log.d(TAG, "木柴不足：体温-20")
        }

        // 如果屋顶漏雪，额外消耗10体温
        if (gameState.roofLeaking) {
            gameState.warmth = (gameState.warmth - 10).coerceAtLeast(0)
            Log.d(TAG, "屋顶漏雪：额外消耗10体温")
        }

        // 每天夜晚体力消耗
        gameState.stamina = (gameState.stamina - 10).coerceAtLeast(0)
        Log.d(TAG, "夜晚体力消耗：体力-10")

        // 前进到下一天
        gameState.currentDay++

        // 记录状态快照
        saveStateSnapshot("夜晚阶段结束，进入第 ${gameState.currentDay} 天")

        val result = NightPhaseResult(
            dayBefore = beforeDay,
            dayAfter = gameState.currentDay,
            firewoodBefore = beforeFirewood,
            firewoodAfter = gameState.firewood,
            firewoodUsed = if (beforeFirewood >= 5) 5 else 0,
            warmthBefore = beforeWarmth,
            warmthAfter = gameState.warmth,
            warmthChange = gameState.warmth - beforeWarmth,
            staminaBefore = beforeStamina,
            staminaAfter = gameState.stamina,
            staminaChange = gameState.stamina - beforeStamina,
            hadEnoughFirewood = beforeFirewood >= 5,
            roofLeaking = gameState.roofLeaking,
            roofLeakingWarmthLoss = if (gameState.roofLeaking) 10 else 0
        )

        Log.d(TAG, "夜晚阶段完成: $result")

        return result
    }

    /**
     * 检查游戏是否结束
     * 如果 warmth <= 0 或 stamina <= 0 或 hope <= 0 或 currentDay > 7，返回 true
     */
    fun checkGameOver(): Boolean {
        val isGameOver = gameState.warmth <= 0 || gameState.stamina <= 0 || gameState.hope <= 0 || gameState.currentDay > 7

        if (isGameOver) {
            val reason = when {
                gameState.warmth <= 0 -> "体温过低"
                gameState.stamina <= 0 -> "体力耗尽"
                gameState.hope <= 0 -> "绝望而死"
                gameState.currentDay > 7 -> getGameOverReason()
                else -> "未知原因"
            }
            Log.d(TAG, "游戏结束检测: $isGameOver, 原因: $reason")
        }

        return isGameOver
    }

    /**
     * 获取游戏结束原因
     */
    fun getGameOverReason(): String {
        return when {
            gameState.warmth <= 0 -> "体温过低"
            gameState.stamina <= 0 -> "体力耗尽"
            gameState.hope <= 0 -> "绝望而死"
            gameState.currentDay > 7 -> {
                // 根据最终状态决定胜利类型
                when {
                    gameState.hope >= 80 && gameState.specialItems.contains("signal_gun") -> "完美救援"
                    gameState.hope >= 70 -> "坚强意志"
                    gameState.hope >= 40 -> "成功存活"
                    else -> "勉强存活"
                }
            }
            else -> "游戏进行中"
        }
    }

    /**
     * 检查是否胜利
     */
    fun isVictory(): Boolean {
        return gameState.currentDay > 7 && gameState.warmth > 0 && gameState.stamina > 0
    }

    /**
     * 前进到下一天
     */
    fun advanceToNextDay() {
        gameState.currentDay++
        saveStateSnapshot("进入第 ${gameState.currentDay} 天")
        Log.d(TAG, "进入第 ${gameState.currentDay} 天")
    }

    /**
     * 保存状态快照
     * @param description 状态描述
     */
    private fun saveStateSnapshot(description: String) {
        val snapshot = GameStateSnapshot(
            timestamp = System.currentTimeMillis(),
            description = description,
            state = gameState.copy()
        )
        gameHistory.add(snapshot)

        // 限制历史记录数量，避免内存泄漏
        if (gameHistory.size > 100) {
            gameHistory.removeAt(0)
        }
    }

    /**
     * 获取游戏历史
     * @return 游戏历史快照列表
     */
    fun getGameHistory(): List<GameStateSnapshot> {
        return gameHistory.toList()
    }

    /**
     * 获取游戏统计信息
     * @return 游戏统计数据
     */
    fun getGameStatistics(): GameStatistics {
        return GameStatistics(
            currentDay = gameState.currentDay,
            totalChoicesMade = gameHistory.size - 1, // 减去初始状态
            specialItemsCollected = gameState.specialItems.size,
            maxWarmthReached = gameHistory.maxOfOrNull { it.state.warmth } ?: gameState.warmth,
            minWarmthReached = gameHistory.minOfOrNull { it.state.warmth } ?: gameState.warmth,
            maxStaminaReached = gameHistory.maxOfOrNull { it.state.stamina } ?: gameState.stamina,
            minStaminaReached = gameHistory.minOfOrNull { it.state.stamina } ?: gameState.stamina,
            totalFirewoodCollected = calculateTotalFirewoodCollected(),
            totalFoodConsumed = calculateTotalFoodConsumed()
        )
    }

    /**
     * 计算总共收集的木柴数量
     */
    private fun calculateTotalFirewoodCollected(): Int {
        var total = 0
        for (i in 1 until gameHistory.size) {
            val current = gameHistory[i].state.firewood
            val previous = gameHistory[i - 1].state.firewood
            if (current > previous) {
                total += (current - previous)
            }
        }
        return total
    }

    /**
     * 计算总共消耗的食物数量
     */
    private fun calculateTotalFoodConsumed(): Int {
        var total = 0
        for (i in 1 until gameHistory.size) {
            val current = gameHistory[i].state.food
            val previous = gameHistory[i - 1].state.food
            if (current < previous) {
                total += (previous - current)
            }
        }
        return total
    }

    /**
     * 重置游戏
     */
    fun resetGame() {
        Log.d(TAG, "重置游戏")
        isInitialized = false
        gameHistory.clear()
        initializeGame()
    }

    /**
     * 选择结果数据类
     */
    data class ChoiceResult(
        val success: Boolean,
        val message: String,
        val stateChanges: Map<String, Pair<Int, Int>>, // 属性名 -> (旧值, 新值)
        val specialItemGained: String? = null,
        val gameOverResult: GameOverResult? = null
    )

    /**
     * 游戏结束结果数据类
     */
    data class GameOverResult(
        val isGameOver: Boolean,
        val isVictory: Boolean,
        val reason: String,
        val finalDay: Int,
        val finalStats: GameState
    )

    /**
     * 夜晚结算结果数据类
     */
    data class NightSettlementResult(
        val firewoodUsed: Int,
        val warmthChange: Int,
        val hadFirewood: Boolean,
        val finalWarmth: Int,
        val finalFirewood: Int
    )

    /**
     * 夜晚阶段结果数据类
     */
    data class NightPhaseResult(
        val dayBefore: Int,
        val dayAfter: Int,
        val firewoodBefore: Int,
        val firewoodAfter: Int,
        val firewoodUsed: Int,
        val warmthBefore: Int,
        val warmthAfter: Int,
        val warmthChange: Int,
        val staminaBefore: Int,
        val staminaAfter: Int,
        val staminaChange: Int,
        val hadEnoughFirewood: Boolean,
        val roofLeaking: Boolean,
        val roofLeakingWarmthLoss: Int
    )

    /**
     * 进食结果数据类
     */
    data class EatResult(
        val success: Boolean,
        val foodUsed: Int,
        val staminaGained: Int,
        val remainingFood: Int
    )

    /**
     * 游戏状态快照数据类
     */
    data class GameStateSnapshot(
        val timestamp: Long,
        val description: String,
        val state: GameState
    )

    /**
     * 游戏统计数据类
     */
    data class GameStatistics(
        val currentDay: Int,
        val totalChoicesMade: Int,
        val specialItemsCollected: Int,
        val maxWarmthReached: Int,
        val minWarmthReached: Int,
        val maxStaminaReached: Int,
        val minStaminaReached: Int,
        val totalFirewoodCollected: Int,
        val totalFoodConsumed: Int
    )
}
